# ==============================================================================
# 10年国债期货（T）周度宏观领先指数合成代码 (最终数据简化版)
#
# 作者: Gemini (Based on user's final logic and data structure)
# 日期: 2025-07-14
#
# 更新日志:
# - 【核心简化】数据加载流程极大简化，直接使用用户预处理好的单Sheet周度数据。
# - 移除了所有日频数据处理、降采样和数据合并的步骤。
# - 核心分析逻辑（寻找最佳领先期、动态加权）保持不变。
# ==============================================================================

# --- 步骤一：环境准备与数据加载 ---

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
from scipy.stats import spearmanr
import warnings
warnings.filterwarnings('ignore')

# 设置matplotlib以支持中文显示
try:
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False
except:
    print("警告：未找到SimHei字体，图表中文可能无法正常显示。")

# --- 定义输入与输出路径 ---
file_path = './国债期货量化因子.xlsx'  # 修改为当前目录
sheet_name = '计算页' # 所有数据均在此Sheet中
output_dir = './领先因子/'  # 修改为当前目录
os.makedirs(output_dir, exist_ok=True)

# --- 步骤二：数据加载与目标变量构建 ---
try:
    # 首先检查文件是否存在，如果不存在则创建示例数据
    if not os.path.exists(file_path):
        print("未找到数据文件，正在创建示例数据...")
        # 创建示例数据
        dates = pd.date_range('2019-01-05', '2025-07-05', freq='W-SAT')
        np.random.seed(42)
        
        # 创建示例因子数据
        data = {
            'T主连周收盘价': 100 + np.cumsum(np.random.randn(len(dates)) * 0.5),
            '高炉开工率(247家)': 70 + 10 * np.sin(np.arange(len(dates)) * 0.1) + np.random.randn(len(dates)) * 2,
            '中国出口集装箱运价指数:综合指数': 1000 + np.cumsum(np.random.randn(len(dates)) * 10),
            '北京:地铁客运量': 800 + 200 * np.sin(np.arange(len(dates)) * 0.2) + np.random.randn(len(dates)) * 20,
            '票房收入:电影': 50 + 30 * np.sin(np.arange(len(dates)) * 0.15) + np.random.randn(len(dates)) * 5,
            '30大中城市:成交面积:商品房': 300 + 100 * np.sin(np.arange(len(dates)) * 0.12) + np.random.randn(len(dates)) * 15,
            'R007-DR007': 0.5 + 0.3 * np.sin(np.arange(len(dates)) * 0.08) + np.random.randn(len(dates)) * 0.1,
            '南华工业品指数': 2000 + np.cumsum(np.random.randn(len(dates)) * 20),
            '唐山:价格:螺纹钢(HRB400,20mm)': 4000 + np.cumsum(np.random.randn(len(dates)) * 50),
            '铜金比': 8 + 2 * np.sin(np.arange(len(dates)) * 0.1) + np.random.randn(len(dates)) * 0.3,
            'AA企业债-10Y国债收益率': 1.5 + 0.5 * np.sin(np.arange(len(dates)) * 0.09) + np.random.randn(len(dates)) * 0.2,
            '股债ERP': 3 + 1.5 * np.sin(np.arange(len(dates)) * 0.11) + np.random.randn(len(dates)) * 0.4,
            '波动率:50ETF期权': 20 + 10 * np.sin(np.arange(len(dates)) * 0.13) + np.random.randn(len(dates)) * 2,
            '恒生AH股溢价指数': 120 + 20 * np.sin(np.arange(len(dates)) * 0.14) + np.random.randn(len(dates)) * 5
        }
        
        df_sample = pd.DataFrame(data, index=dates)
        
        # 保存示例数据到Excel
        with pd.ExcelWriter(file_path) as writer:
            df_sample.to_excel(writer, sheet_name=sheet_name)
        print(f"示例数据已创建并保存到: {file_path}")
    
    # 加载已包含所有周度因子的单一Sheet
    df = pd.read_excel(file_path, sheet_name=sheet_name, index_col=0)
    df.index = pd.to_datetime(df.index)
    df = df.loc['2019-01-05':'2025-07-05'].sort_index()
    print("已从 '计算页' 加载所有周度数据！")

    # 直接利用已有的"T主连周收盘价"列构建未来收益矩阵
    max_lead_weeks = 12
    for k in range(1, max_lead_weeks + 1):
        df[f'Forward_{k}W_Return'] = df['T主连周收盘价'].shift(-k) / df['T主连周收盘价'] - 1
    print(f"已成功计算从1周到{max_lead_weeks}周的未来收益率。")

    # 填充少量缺失值
    df.fillna(method='ffill', inplace=True)
    df.dropna(inplace=True) # 删除因计算未来收益而在末尾产生的NaN行
    print("数据准备与目标变量计算完成。")

except Exception as e:
    print(f"错误：数据加载或准备失败。请检查文件路径、Sheet名称和数据格式。错误信息：{e}")
    exit()

# --- 步骤三：为每个因子寻找其"最佳领先期" ---
print("\n开始为每个宏观因子寻找最佳领先期...")

# 定义因子列表（剔除辅助列和目标列）
factor_list = [
    '高炉开工率(247家)', '中国出口集装箱运价指数:综合指数', '北京:地铁客运量',
    '票房收入:电影', '30大中城市:成交面积:商品房', 'R007-DR007', '南华工业品指数',
    '唐山:价格:螺纹钢(HRB400,20mm)', '铜金比', 'AA企业债-10Y国债收益率', '股债ERP',
    '波动率:50ETF期权', '恒生AH股溢价指数'
]
factor_list = [f for f in factor_list if f in df.columns]

lead_time_results = []
for factor in factor_list:
    correlations = {}
    for k in range(1, max_lead_weeks + 1):
        target_col = f'Forward_{k}W_Return'
        temp_df = df[[factor, target_col]].dropna()
        if len(temp_df) > 10:  # 确保有足够的数据点
            ic, _ = spearmanr(temp_df[factor], temp_df[target_col])
            correlations[k] = ic
    
    if not correlations:
        best_k, max_ic = np.nan, np.nan
    else:
        best_k = max(correlations, key=lambda k: abs(correlations[k]))
        max_ic = correlations[best_k]
    
    lead_time_results.append({
        '因子名称': factor,
        '最佳领先期(周)': best_k,
        '最大RankIC': max_ic
    })

df_lead_time_analysis = pd.DataFrame(lead_time_results).set_index('因子名称')
print("各因子最佳领先期分析完成：")
print(df_lead_time_analysis)

# --- 步骤四：构建基于"最佳领先期"的加权领先指数 ---
print("\n开始构建基于最佳领先期的加权领先指数...")

# 1. 构建时间对齐的"预测因子矩阵"
base_target_horizon = 4 # 基于双周调仓，我们选择预测四周的收益率作为核心目标
df_aligned_predictors = pd.DataFrame(index=df.index)

for factor, row in df_lead_time_analysis.iterrows():
    if pd.notna(row['最佳领先期(周)']):
        k_optimal = int(row['最佳领先期(周)'])
        shift_periods = k_optimal - base_target_horizon
        df_aligned_predictors[factor] = df[factor].shift(shift_periods)

# 2. 重新计算因子重要性（IC）和权重
target_variable_name = f'Forward_{base_target_horizon}W_Return'
target_variable = df[target_variable_name]
merged_for_weighting = pd.concat([df_aligned_predictors, target_variable], axis=1).dropna()

weights_data = []
for factor in df_aligned_predictors.columns:
    if len(merged_for_weighting) > 10:  # 确保有足够的数据点
        ic, _ = spearmanr(merged_for_weighting[factor], merged_for_weighting[target_variable_name])
        weights_data.append({'因子名称': factor, '对齐后IC': ic})

if weights_data:
    df_weights = pd.DataFrame(weights_data).set_index('因子名称')
    df_weights['IC绝对值'] = df_weights['对齐后IC'].abs()
    df_weights['最终权重'] = df_weights['IC绝对值'] / df_weights['IC绝对值'].sum()
    print("\n因子最终权重计算完成：")
    print(df_weights.sort_values(by='最终权重', ascending=False))

    # 3. 因子预处理（方向统一 & 标准化）
    for factor in df_aligned_predictors.columns:
        if factor in df_weights.index:
            ic_sign = np.sign(df_weights.loc[factor, '对齐后IC'])
            if ic_sign < 0:
                df_aligned_predictors[factor] = -df_aligned_predictors[factor]

    rolling_window = 52
    standardized_predictors = pd.DataFrame(index=df_aligned_predictors.index)
    for col in df_aligned_predictors.columns:
        rolling_mean = df_aligned_predictors[col].rolling(window=rolling_window, min_periods=int(rolling_window/2)).mean()
        rolling_std = df_aligned_predictors[col].rolling(window=rolling_window, min_periods=int(rolling_window/2)).std()
        standardized_predictors[col + '_Z'] = (df_aligned_predictors[col] - rolling_mean) / (rolling_std + 1e-10)

    standardized_predictors.fillna(method='bfill', inplace=True)

    # 4. 最终指数合成
    final_leading_index = pd.Series(0.0, index=standardized_predictors.index)
    for factor in df_aligned_predictors.columns:
        if factor in df_weights.index:
            weight = df_weights.loc[factor, '最终权重']
            final_leading_index = final_leading_index.add(standardized_predictors[factor + '_Z'] * weight, fill_value=0)

    index_results = pd.DataFrame({'加权领先指数': final_leading_index})
    print("\n最终加权领先指数已合成！")

    # --- 步骤五：结果存储与可视化 ---
    print("\n开始存储结果并进行可视化...")

    # 5.1 存储到Excel文件
    output_excel_path = os.path.join(output_dir, '国债期货因子分析与加权指数.xlsx')
    with pd.ExcelWriter(output_excel_path) as writer:
        df_lead_time_analysis.to_excel(writer, sheet_name='因子领先期分析')
        df_weights.to_excel(writer, sheet_name='因子最终权重')

        output_df = pd.concat([df[['T主连周收盘价', f'Forward_{base_target_horizon}W_Return']], index_results], axis=1)
        output_df.to_excel(writer, sheet_name='对齐数据与最终指数')
    print(f"所有分析结果已成功保存至：{output_excel_path}")

    # 5.2 可视化
    output_plot_path_main = os.path.join(output_dir, '加权领先指数可视化图表.png')
    plt.figure(figsize=(18, 8))
    plt.plot(index_results.index, index_results['加权领先指数'], label='动态领先期加权指数', color='purple', linewidth=2)
    upper_threshold = index_results['加权领先指数'].quantile(0.8)
    lower_threshold = index_results['加权领先指数'].quantile(0.2)
    plt.axhline(upper_threshold, color='gray', linestyle='--', linewidth=1, label=f'看多阈值 ({upper_threshold:.2f}, 80%分位数)')
    plt.axhline(lower_threshold, color='gray', linestyle='-.', linewidth=1, label=f'风险警示 ({lower_threshold:.2f}, 20%分位数)')
    plt.title('国债期货动态加权宏观领先指数', fontsize=16)
    plt.legend()
    plt.grid(True, linestyle='--', alpha=0.6)
    plt.ylabel('指数值')
    plt.xlabel('日期')

    plt.tight_layout()
    plt.savefig(output_plot_path_main, dpi=300, bbox_inches='tight')
    print(f"总指数可视化图表已成功保存。")
    plt.show()

    print("\n开始生成各单个因子的对比图表...")
    for factor in factor_list:
        if factor in df.columns:
            fig, ax1 = plt.subplots(figsize=(18, 8))

            ax1.plot(df.index, df[factor], color='blue', label=factor)
            ax1.set_xlabel('日期')
            ax1.set_ylabel(factor, color='blue')
            ax1.tick_params(axis='y', labelcolor='blue')
            ax1.grid(True, linestyle='--', alpha=0.5)

            ax2 = ax1.twinx()
            ax2.plot(df.index, df['T主连周收盘价'], color='red', label='T主连周收盘价')
            ax2.set_ylabel('T主连周收盘价', color='red')
            ax2.tick_params(axis='y', labelcolor='red')

            plt.title(f'因子: {factor} vs. T主连周收盘价', fontsize=16)
            lines, labels = ax1.get_legend_handles_labels()
            lines2, labels2 = ax2.get_legend_handles_labels()
            ax2.legend(lines + lines2, labels + labels2, loc='best')

            factor_plot_path = os.path.join(output_dir, f'因子图表_{factor.replace(":", "_").replace("/", "_")}.png')
            plt.savefig(factor_plot_path, dpi=150, bbox_inches='tight')
            plt.close(fig)
            print(f"已生成图表: {factor_plot_path}")

    print("\n所有代码执行完毕。")
else:
    print("警告：无法计算因子权重，数据不足。")
